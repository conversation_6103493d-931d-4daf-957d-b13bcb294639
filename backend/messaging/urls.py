# backend/messaging/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('conversations/', views.list_conversations, name='conversation-list'),
    path('conversations/create/', views.create_conversation, name='create-conversation'),
    path('conversations/<uuid:conversation_id>/messages/', views.get_conversation_messages, name='conversation-messages'),
    path('conversations/<uuid:conversation_id>/send/', views.send_message, name='send-message'),
    path('users/search/', views.search_users, name='search-users'),
    path('users/<uuid:user_id>/', views.get_user_profile, name='get-user-profile'),
]
