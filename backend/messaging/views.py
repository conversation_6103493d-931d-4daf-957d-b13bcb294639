# backend/messaging/views.py
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.db.models import Q, Max
from django.utils import timezone
from django.contrib.auth import get_user_model
from pydantic_core import ValidationError
from .models import Conversation, ConversationParticipant, Message
from .schemas import (
    ConversationCreate, ConversationResponse, MessageCreate,
    MessageResponse, UserBasic
)
User = get_user_model()

class MessagePagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100

def serialize_user(user):
    """Convert Django User model to Pydantic UserBasic"""
    return UserBasic(
        id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        profile_picture=user.profile_picture
    )

def serialize_message(message, request_user=None):
    """Convert Django Message model to Pydantic MessageResponse"""
    # Get message status for the requesting user
    status = None
    if request_user and request_user.is_authenticated:
        try:
            message_status = message.statuses.filter(user=request_user).first()
            if message_status:
                status = message_status.status
            elif message.sender == request_user:
                # For own messages, default to SENT if no status exists
                status = 'SENT'
        except Exception:
            pass

    return MessageResponse(
        id=message.id,
        conversation_id=message.conversation.id,
        sender=serialize_user(message.sender),
        content=message.content,
        message_type=message.message_type,
        created_at=message.created_at,
        updated_at=message.updated_at,
        status=status
    )

def serialize_conversation(conversation, request_user=None):
    """Convert Django Conversation model to Pydantic ConversationResponse"""
    participants = [
        serialize_user(p.user) for p in conversation.participants.all()
    ]

    last_message = conversation.messages.last()
    last_message_data = serialize_message(last_message, request_user) if last_message else None

    return ConversationResponse(
        id=conversation.id,
        type=conversation.type,
        name=conversation.name,
        participants=participants,
        last_message=last_message_data,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def list_conversations(request):
    """List user's conversations"""
    conversations = Conversation.objects.filter(
        participants__user=request.user
    ).distinct().order_by('-updated_at')

    paginator = MessagePagination()
    page = paginator.paginate_queryset(conversations, request)

    conversation_data = [
        serialize_conversation(conv, request.user).model_dump()
        for conv in page
    ]

    return paginator.get_paginated_response(conversation_data)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_conversation(request):
    """Create a new conversation"""
    try:
        # Validate input with Pydantic
        conversation_data = ConversationCreate(**request.data)

        # For direct messages, check if conversation already exists
        if conversation_data.type == 'DIRECT' and len(conversation_data.participant_ids) == 1:
            other_user_id = conversation_data.participant_ids[0]
            existing_conversation = Conversation.objects.filter(
                type='DIRECT',
                participants__user=request.user
            ).filter(
                participants__user_id=other_user_id
            ).first()

            if existing_conversation:
                return Response(
                    serialize_conversation(existing_conversation, request.user).model_dump(),
                    status=status.HTTP_200_OK
                )

        # Verify all participant IDs exist
        participant_users = User.objects.filter(id__in=conversation_data.participant_ids)
        if participant_users.count() != len(conversation_data.participant_ids):
            return Response(
                {'error': 'One or more participant IDs are invalid'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create new conversation
        conversation = Conversation.objects.create(
            type=conversation_data.type,
            name=conversation_data.name if conversation_data.type == 'GROUP' else None
        )

        # Add creator as participant
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=request.user,
            role='ADMIN' if conversation_data.type == 'GROUP' else 'MEMBER'
        )

        # Add other participants
        for user_id in conversation_data.participant_ids:
            if user_id != request.user.id:
                ConversationParticipant.objects.create(
                    conversation=conversation,
                    user_id=user_id,
                    role='MEMBER'
                )

        return Response(
            serialize_conversation(conversation, request.user).model_dump(),
            status=status.HTTP_201_CREATED
        )

    except ValidationError as e:
        # Convert validation errors to a JSON-serializable format
        errors = []
        for error in e.errors():
            error_dict = {
                'type': error.get('type'),
                'loc': error.get('loc', []),
                'msg': error.get('msg'),
                'input': str(error.get('input', ''))
            }
            errors.append(error_dict)
        return Response({'errors': errors}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_conversation_messages(request, conversation_id):
    """Get messages for a conversation"""
    conversation = get_object_or_404(Conversation, id=conversation_id)

    # Check if user is participant
    if not conversation.participants.filter(user=request.user).exists():
        return Response(
            {'error': 'Not a participant in this conversation'},
            status=status.HTTP_403_FORBIDDEN
        )

    messages = conversation.messages.order_by('-created_at')

    paginator = MessagePagination()
    page = paginator.paginate_queryset(messages, request)

    message_data = [serialize_message(msg, request.user).model_dump() for msg in page]

    return paginator.get_paginated_response(message_data)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_message(request, conversation_id):
    """Send a message to a conversation"""
    try:
        conversation = get_object_or_404(Conversation, id=conversation_id)

        # Check if user is participant
        participant = conversation.participants.filter(user=request.user).first()
        if not participant:
            return Response(
                {'error': 'Not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate input with Pydantic
        message_data = MessageCreate(**request.data)

        # Create message
        message = Message.objects.create(
            conversation=conversation,
            sender=request.user,
            content=message_data.content,
            message_type=message_data.message_type
        )

        # Update conversation timestamp
        conversation.updated_at = timezone.now()
        conversation.save(update_fields=['updated_at'])

        # Note: Real-time emission will be handled by the socket server
        # when it detects the new message in the database

        return Response(
            serialize_message(message, request.user).model_dump(),
            status=status.HTTP_201_CREATED
        )

    except Http404:
        return Response({'error': 'Conversation not found'}, status=status.HTTP_404_NOT_FOUND)
    except ValidationError as e:
        # Convert validation errors to a JSON-serializable format
        errors = []
        for error in e.errors():
            error_dict = {
                'type': error.get('type'),
                'loc': error.get('loc', []),
                'msg': error.get('msg'),
                'input': str(error.get('input', ''))
            }
            errors.append(error_dict)
        return Response({'errors': errors}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_users(request):
    """Search for users by username, first name, or last name"""
    query = request.GET.get('q', '').strip()

    if not query:
        return Response({
            'success': True,
            'results': []
        }, status=status.HTTP_200_OK)

    if len(query) < 2:
        return Response({
            'success': False,
            'error': 'Search query must be at least 2 characters long'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Search users by username, first name, or last name
        # Exclude the current user from results
        users = User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        ).exclude(
            id=request.user.id
        ).order_by('username')[:10]  # Limit to 10 results

        # Serialize users
        user_data = []
        for user in users:
            user_data.append({
                'id': str(user.id),
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'profile_picture': user.profile_picture,
                'full_name': f"{user.first_name} {user.last_name}".strip()
            })

        return Response({
            'success': True,
            'results': user_data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_user_profile(request, user_id):
    """Get a user's profile by ID"""
    try:
        # Get user by ID
        user = get_object_or_404(User, id=user_id)

        # Serialize user data
        user_data = {
            'id': str(user.id),
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'profile_picture': user.profile_picture,
            'full_name': f"{user.first_name} {user.last_name}".strip()
        }

        return Response({
            'success': True,
            'data': user_data
        }, status=status.HTTP_200_OK)

    except Http404:
        return Response({
            'success': False,
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
