// socket-server/src/services/messageService.ts
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { MessageCreateSchema, MessageCreate } from '../schemas';

export class MessageService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async createMessage(data: MessageCreate, senderId: string) {
    try {
      // Validate input
      const validatedData = MessageCreateSchema.parse(data);

      // Verify user has access to conversation
      const participant = await this.verifyConversationAccess(
        senderId,
        validatedData.conversationId
      );

      if (!participant) {
        throw new Error('Access denied to conversation');
      }

      // Create message in database
      const message = await this.prisma.message.create({
        data: {
          conversationId: validatedData.conversationId,
          senderId: senderId,
          content: validatedData.content,
          messageType: validatedData.messageType
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          }
        }
      });

      // Update conversation timestamp
      await this.prisma.conversation.update({
        where: { id: validatedData.conversationId },
        data: { updatedAt: new Date() }
      });

      return message;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  async getConversationMessages(conversationId: string, userId: string, page: number = 1, limit: number = 50) {
    try {
      // Verify user has access to conversation
      const hasAccess = await this.verifyConversationAccess(userId, conversationId);
      if (!hasAccess) {
        throw new Error('Access denied to conversation');
      }

      const skip = (page - 1) * limit;

      const messages = await this.prisma.message.findMany({
        where: {
          conversationId: conversationId
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      });

      return messages.reverse(); // Return in chronological order
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  }

  async verifyConversationAccess(userId: string, conversationId: string) {
    try {
      const participant = await this.prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          userId
        }
      });
      return participant;
    } catch (error) {
      console.error('Error verifying conversation access:', error);
      return null;
    }
  }

  async updateUserStatus(userId: string, isOnline: boolean) {
    try {
      await this.prisma.user.update({
        where: { id: userId },
        data: { lastSeen: new Date() }
      });
      return true;
    } catch (error) {
      console.error('Error updating user status:', error);
      return false;
    }
  }

  async getMessageById(messageId: string) {
    try {
      const message = await this.prisma.message.findUnique({
        where: { id: messageId },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          }
        }
      });

      return message;
    } catch (error) {
      console.error('Error getting message by ID:', error);
      throw error;
    }
  }
}
