// socket-server/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              String   @id @default(uuid()) @db.Uuid
  password        String
  last_login      DateTime?
  is_superuser    <PERSON>olean  @default(false)
  email           String   @unique
  username        String   @unique
  firstName       String   @map("first_name")
  lastName        String   @map("last_name")
  profilePicture  String?  @map("profile_picture")
  isVerified      <PERSON>olean  @default(false) @map("is_verified")
  is_staff        <PERSON><PERSON>an  @default(false)
  is_active       <PERSON>olean  @default(true)
  date_joined     DateTime @default(now())
  lastSeen        DateTime @updatedAt @map("last_seen")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  sentMessages     Message[] @relation("MessageSender")
  conversations    ConversationParticipant[]
  messageStatuses  MessageStatus[] @relation("MessageStatuses")

  @@map("users")
}

model Conversation {
  id          String   @id @default(uuid()) @db.Uuid
  type        String   @default("DIRECT") // DIRECT or GROUP
  name        String?  // For group chats
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  participants ConversationParticipant[]
  messages     Message[]

  @@map("conversations")
}

model ConversationParticipant {
  id             String   @id @default(uuid()) @db.Uuid
  conversationId String   @map("conversation_id") @db.Uuid
  userId         String   @map("user_id") @db.Uuid
  role           String   @default("MEMBER") // ADMIN or MEMBER
  joinedAt       DateTime @default(now()) @map("joined_at")

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@unique([conversationId, userId])
  @@map("conversation_participants")
}

model Message {
  id             String   @id @default(uuid()) @db.Uuid
  conversationId String   @map("conversation_id") @db.Uuid
  senderId       String   @map("sender_id") @db.Uuid
  content        String
  messageType    String   @default("TEXT") @map("message_type") // TEXT, IMAGE, FILE, SYSTEM
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  conversation Conversation      @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender       User              @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  statuses     MessageStatus[]

  @@map("messages")
}

model MessageStatus {
  id        String            @id @default(uuid()) @db.Uuid
  messageId String            @map("message_id") @db.Uuid
  userId    String            @map("user_id") @db.Uuid
  status    MessageStatusType @default(SENT)
  createdAt DateTime          @default(now()) @map("created_at")
  updatedAt DateTime          @updatedAt @map("updated_at")

  // Relations
  message Message @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user    User    @relation("MessageStatuses", fields: [userId], references: [id], onDelete: Cascade)

  // Each user can have only one status per message
  @@unique([messageId, userId])
  @@map("message_statuses")
}

enum MessageStatusType {
  SENT
  DELIVERED
  READ
  FAILED
}
